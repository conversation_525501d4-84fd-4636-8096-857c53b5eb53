import { useState, useEffect } from 'react';
import { useForm } from '@tanstack/react-form';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { z } from 'zod';
import { Link, useNavigate } from '@tanstack/react-router';
import { supabase } from '../../lib/supabase';
import { useCertificate } from '../../contexts/CertificateContext';

// Define the Fenster schema for reuse
const fensterSchema = z.object({
  id: z.string(),
  bezeichnung: z.string().min(1, 'Bezeichnung ist erforderlich'),
  art: z.string().optional(),
  flaeche: z.string().min(1, 'Fläche ist erforderlich'),
  uWert: z.string().optional(),
  ausrichtung: z.string().optional(),
  baujahr: z.string().optional(),
});

// Define the form schema using Zod
const fensterPageSchema = z.object({
  // Fenster (dynamisch)
  fenster: z.array(fensterSchema).default([]),
});

type FensterFormValues = z.infer<typeof fensterPageSchema>;
type Fenster = z.infer<typeof fensterSchema>;

export const FensterPage = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { activeCertificateId } = useCertificate();

  // Default Fenster
  const defaultFenster = [
    {
      id: '1',
      bezeichnung: 'Fenster1',
      art: 'fb_KunststoffIsoliert',
      flaeche: '',
      uWert: '',
      ausrichtung: '0',
      baujahr: ''
    }
  ];

  // State für dynamische Fenster
  const [fenster, setFenster] = useState<Fenster[]>(defaultFenster);

  // Initial form values
  const [initialValues, setInitialValues] = useState<Partial<FensterFormValues>>({
    fenster: fenster,
  });

  // Fetch certificate type to check if user should be on this page
  const { data: certificateData } = useQuery({
    queryKey: ['energieausweise', 'certificate_type', activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      const { data, error } = await supabase
        .from('energieausweise')
        .select('certificate_type')
        .eq('id', activeCertificateId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    enabled: !!activeCertificateId,
    retry: false,
  });

  // Redirect if certificate type is not WG/B
  useEffect(() => {
    if (certificateData && certificateData.certificate_type !== 'WG/B') {
      navigate({ to: '/erfassen' });
    }
  }, [certificateData, navigate]);

  // Fetch existing data
  const { data: existingData, isError, error } = useQuery({
    queryKey: ['energieausweise', 'fenster', activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      const { data, error } = await supabase
        .from('energieausweise')
        .select('fenster')
        .eq('id', activeCertificateId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    enabled: !!activeCertificateId,
    retry: false,
  });

  // Update form values when data is fetched
  useEffect(() => {
    if (existingData && existingData.fenster) {
      const data = existingData.fenster;

      // Update form values
      setInitialValues(prev => ({
        ...prev,
        ...(typeof data === 'object' && data !== null && !Array.isArray(data) ? data : {}),
      }));

      // Update dynamic components
      if (typeof data === 'object' && data !== null && !Array.isArray(data) && data.fenster && Array.isArray(data.fenster)) {
        const parsedFenster = data.fenster
          .map((item: any) => fensterSchema.safeParse(item))
          .filter((result: any) => result.success)
          .map((result: any) => result.data);
        setFenster(parsedFenster);
      }
    }
    setIsLoading(false);
  }, [existingData]);

  // Define the mutation for saving data to Supabase
  const saveMutation = useMutation({
    mutationFn: async (data: FensterFormValues) => {
      if (!activeCertificateId) throw new Error('Kein aktives Zertifikat ausgewählt.');

      const { data: result, error } = await supabase
        .from('energieausweise')
        .update({
          fenster: data,
          updated_at: new Date().toISOString(),
        })
        .eq('id', activeCertificateId)
        .select();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['energieausweise', activeCertificateId] });
      queryClient.invalidateQueries({ queryKey: ['energieausweise', 'fenster', activeCertificateId] });
      // Navigate to the next page
      navigate({ to: '/erfassen/heizung' });
    },
    onError: (error) => {
      setSubmitError(`Fehler beim Speichern: ${error.message}`);
    },
  });

  // Create the form
  const form = useForm({
    defaultValues: initialValues,
    onSubmit: async ({ value }) => {
      setSubmitError(null);
      saveMutation.mutate(value as FensterFormValues);
    },
  });

  // Fenster-Komponente für dynamische Fenster
  const FensterField = ({
    fenster,
    index,
    onUpdate,
    onRemove
  }: {
    fenster: Fenster;
    index: number;
    onUpdate: (index: number, field: keyof Fenster, value: string) => void;
    onRemove: (index: number) => void;
  }) => {
    return (
      <div className="p-4 mb-4 border rounded-md bg-gray-50">
        <div className="flex justify-between mb-2">
          <h4 className="font-medium">Fenster {index + 1}</h4>
          {index > 0 && (
            <button
              type="button"
              onClick={() => onRemove(index)}
              className="text-red-500 hover:text-red-700"
            >
              Entfernen
            </button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="mb-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Bezeichnung <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={fenster.bezeichnung}
              onChange={(e) => onUpdate(index, 'bezeichnung', e.target.value)}
              className="w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 border-gray-300"
              placeholder="z.B. Dachflächenfenster"
            />
          </div>

          <div className="mb-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Art
            </label>
            <select
              value={fenster.art || ''}
              onChange={(e) => onUpdate(index, 'art', e.target.value)}
              className="w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 border-gray-300"
            >
              <option value="fb_HolzEinfach">Holz einfach</option>
              <option value="fb_HolzIsoliert">Holz isoliert</option>
              <option value="fb_KunststoffEinfach">Kunststoff einfach</option>
              <option value="fb_KunststoffIsoliert">Kunststoff isoliert</option>
              <option value="fb_KunststoffWSG">Kunststoff mit Wärmeschutzverglasung</option>
              <option value="fb_MetallEinfach">Metall einfach</option>
              <option value="fb_MetallIsoliert">Metall isoliert</option>
              <option value="fb_MetallWSG">Metall mit Wärmeschutzverglasung</option>
            </select>
          </div>

          <div className="mb-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Fläche in m² <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={fenster.flaeche}
              onChange={(e) => onUpdate(index, 'flaeche', e.target.value)}
              className="w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 border-gray-300"
              placeholder="z.B. 8,1"
            />
          </div>

          <div className="mb-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              U-Wert in W/m²K
            </label>
            <input
              type="text"
              value={fenster.uWert}
              onChange={(e) => onUpdate(index, 'uWert', e.target.value)}
              className="w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 border-gray-300"
              placeholder="z.B. 3,0"
            />
          </div>

          <div className="mb-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Ausrichtung (Grad)
            </label>
            <select
              value={fenster.ausrichtung || '0'}
              onChange={(e) => onUpdate(index, 'ausrichtung', e.target.value)}
              className="w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 border-gray-300"
            >
              <option value="0">Nord (0°)</option>
              <option value="45">Nordost (45°)</option>
              <option value="90">Ost (90°)</option>
              <option value="135">Südost (135°)</option>
              <option value="180">Süd (180°)</option>
              <option value="225">Südwest (225°)</option>
              <option value="270">West (270°)</option>
              <option value="315">Nordwest (315°)</option>
            </select>
          </div>

          <div className="mb-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Baujahr / Jahr des letzten Austauschs
            </label>
            <input
              type="text"
              value={fenster.baujahr}
              onChange={(e) => onUpdate(index, 'baujahr', e.target.value)}
              className="w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 border-gray-300"
              placeholder="z.B. 2010"
            />
          </div>
        </div>
      </div>
    );
  };

  // Funktionen zum Hinzufügen und Entfernen von Fenstern
  const addFenster = () => {
    const newFenster = [...fenster];
    const newId = (newFenster.length + 1).toString();
    newFenster.push({
      id: newId,
      bezeichnung: `Fenster${newId}`,
      art: 'fb_KunststoffIsoliert',
      flaeche: '',
      uWert: '',
      ausrichtung: '0',
      baujahr: ''
    });
    setFenster(newFenster);
    form.setFieldValue('fenster', newFenster);
  };

  const removeFenster = (index: number) => {
    const newFenster = [...fenster];
    newFenster.splice(index, 1);
    setFenster(newFenster);
    form.setFieldValue('fenster', newFenster);
  };

  const updateFenster = (index: number, field: keyof Fenster, value: string) => {
    const newFenster = [...fenster];
    newFenster[index] = { ...newFenster[index], [field]: value };
    setFenster(newFenster);
    form.setFieldValue('fenster', newFenster);
  };

  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-800 mb-6">
        Fenster erfassen
      </h1>
      <p className="text-lg text-gray-600 mb-8">
        Bitte geben Sie die Daten zu den Fenstern des Gebäudes ein.
      </p>

      {isLoading ? (
        <div className="bg-white shadow-md rounded-lg p-6 flex justify-center items-center h-64">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-green-500 mb-2"></div>
            <p className="text-gray-600">Daten werden geladen...</p>
          </div>
        </div>
      ) : (
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
          className="bg-white shadow-md rounded-lg p-6"
        >
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
              Fenster
            </h2>
            {fenster.map((fensterItem, index) => (
              <FensterField
                key={fensterItem.id}
                fenster={fensterItem}
                index={index}
                onUpdate={updateFenster}
                onRemove={removeFenster}
              />
            ))}
            <button
              type="button"
              onClick={addFenster}
              className="mt-2 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
            >
              Fenster hinzufügen
            </button>
          </div>

          {submitError && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {submitError}
            </div>
          )}

          <div className="flex justify-between mt-8">
            <Link
              to="/erfassen/gebaeudedetails2"
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
            >
              Zurück
            </Link>
            <button
              type="submit"
              disabled={form.state.isSubmitting || saveMutation.isPending}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:bg-green-300"
            >
              {form.state.isSubmitting || saveMutation.isPending ? 'Wird gespeichert...' : 'Weiter'}
            </button>
          </div>
        </form>
      )}

      {isError && (
        <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          <p>Fehler beim Laden der Daten: {error instanceof Error ? error.message : 'Unbekannter Fehler'}</p>
          <p className="mt-2">Bitte versuchen Sie es später erneut oder kontaktieren Sie den Support.</p>
        </div>
      )}
    </div>
  );
};
